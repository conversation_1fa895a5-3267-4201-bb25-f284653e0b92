# saymee_flutter

A new Flutter project.

# Figma
https://www.figma.com/design/YQuVk1tfBXkOzoC6X11gV3/Saymee-App-2025?m=auto&t=6dFi4z8mxKb4uOzN-6
# Task, issues


Saymee - <PERSON>hông đăng nhập - Data tĩnh
- Trang chủ: Hu<PERSON>
- <PERSON><PERSON> chiếu: <PERSON><PERSON>
    - <PERSON><PERSON>p tiên
- Đ<PERSON>ng bọn: Đăng
    - <PERSON><PERSON> màu
- <PERSON><PERSON><PERSON> hà<PERSON>: Đăng
- Menu: Đăng
    - Tiện ích
    - Cài đặt
* Dialog thông báo: Huy
* Hàm điều hướng đi các màn chung: Huy. navigator_helper.dart
* Nút dùng chung: Đăng
 

# Dự án JESTER BOT APP

## Hướng dẫn build project

### Android

#### APK

- **Debug**:  

  ```bash
  flutter build apk --debug --flavor DEV -t lib/main.dart --split-per-abi
  ```

- **Release**:  

  ```bash
  flutter build apk --release --flavor DEV -t lib/main.dart
  ```

#### App Bundle

- **Debug**:  

  ```bash
  flutter build appbundle --debug --flavor DEV -t lib/main.dart --split-per-abi
  ```

- **Release**:  

  ```bash
  flutter build appbundle --release --flavor DEV -t lib/main.dart
  ```

### iOS

- **Debug**:  

  ```bash
  flutter build ios --debug --flavor DEV -t lib/main.dart
  ```

- **Release**:  

  ```bash
  flutter build ios --release --flavor DEV -t lib/main.dart
  ```

- **IPA**:  

  ```bash
  flutter build ipa --export-options-plist=./ios/Runner/exportOption.plist --flavor DEV --release
  ```

---

## Hướng dẫn chạy project

1. **Bước 1**: Chạy lệnh:  

   ```bash
   flutter pub get
   ```  

   để cài thư viện.

2. **Bước 2**: Đảm bảo cấu hình môi trường phát triển (nếu cần).

3. **Bước 3**: Chạy lệnh:  

   ```bash
   flutter gen-l10n
   ```  

   để tạo file ngôn ngữ sau khi thêm text mới.

4. **Bước 4**: Chạy lệnh:  

   ```bash
   flutter run lib/main.dart --flavor DEV
   ```  

   để chạy project.

## Hướng dẫn cấu hình project


## Danh sách các màn hình theo tên file



## Danh sách các service



## Các Bloc quan trọng