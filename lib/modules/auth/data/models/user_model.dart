import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final int? userId;
  final String? fullname;
  final String? phone;
  final String? nickname;
  final String? avatar;
  final String? apisecret;
  final String? refreshToken;

  const UserModel({
    this.userId,
    this.fullname,
    this.phone,
    this.nickname,
    this.avatar,
    this.apisecret,
    this.refreshToken,
  });

  @override
  List<Object?> get props => [userId, apisecret];
}
