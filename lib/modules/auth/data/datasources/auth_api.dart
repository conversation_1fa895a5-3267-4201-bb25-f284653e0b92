import 'package:dio/dio.dart';
import 'package:saymee_flutter/core/utils/globals.dart';
import 'package:saymee_flutter/core/utils/utils.dart';

class AuthApi {
  final dioClient = Utils.dioClient;

  // ---- methods ----
  Future<Response> getLoginOtp({required String phone}) async {
    const String url = '/auth/getloginotp';
    final params = {"phone": phone};

    Utils.debugLog(params);

    try {
      final response = await dioClient.post(
        url,
        data: params,
        options: Options(headers: {"apisecret": Globals.appSecret}),
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }
}
