import 'package:equatable/equatable.dart';
import 'package:saymee_flutter/modules/auth/data/models/user_model.dart';

final class AuthState extends Equatable {
  UserModel? user;
  int counter = 0;

  AuthState._({this.user, this.counter = 0});

  @override
  List<Object?> get props => [user, counter];

  AuthState.initial() : this._();
  AuthState.setUserInfo({required UserModel this.user}) {
    counter++;
  }
}
