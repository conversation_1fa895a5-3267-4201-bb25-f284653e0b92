import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee_flutter/core/helpers/shared_preference_helper.dart';
import 'package:saymee_flutter/modules/auth/data/repositories/auth_repository.dart';

import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository repository;

  final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();

  AuthBloc({required this.repository}) : super(AuthState.initial()) {
    on<AuthEvent>((event, emit) async {
      if (event is AuthSetUserInfo) {
        final user = event.user;
        emit(AuthState.setUserInfo(user: user));
      }
    });
  }
}
