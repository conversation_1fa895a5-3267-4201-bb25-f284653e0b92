import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee_flutter/core/components/app_annotated_region.dart';
import 'package:saymee_flutter/core/components/app_otp_dialog.dart';
import 'package:saymee_flutter/core/components/buttons/primary_button.dart';
import 'package:saymee_flutter/core/components/buttons/secondary_button.dart';
import 'package:saymee_flutter/core/components/containers/background.dart';
import 'package:saymee_flutter/core/components/inputs/text_input.dart';
import 'package:saymee_flutter/core/constants/app_dimensions.dart';
import 'package:saymee_flutter/core/constants/app_icons.dart';
import 'package:saymee_flutter/core/constants/app_images.dart';
import 'package:saymee_flutter/core/constants/app_stores.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:saymee_flutter/core/constants/app_validator.dart';
import 'package:saymee_flutter/core/extensions/localized_extension.dart';
import 'package:saymee_flutter/core/extensions/num_extension.dart';
import 'package:saymee_flutter/core/helpers/shared_preference_helper.dart';
import 'package:saymee_flutter/core/utils/utils.dart';
import 'package:saymee_flutter/modules/auth/data/datasources/auth_api.dart';
import 'package:saymee_flutter/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee_flutter/modules/auth/presentation/blocs/auth_bloc.dart';
import 'package:saymee_flutter/modules/auth/presentation/blocs/auth_state.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  final _formKey = GlobalKey<FormState>();

  final _authBloc = Modular.get<AuthBloc>();
  final _preHelper = Modular.get<SharedPreferenceHelper>();
  final _authRespository = Modular.get<AuthRepository>();

  final _phoneController = TextEditingController();

  @override
  void initState() {
    final localPhone = _preHelper.get(key: AppStores.kPhone);

    if (localPhone != null) {
      _phoneController.text = localPhone.toString();
    }
    super.initState();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  _listener() {
    return [BlocListener<AuthBloc, AuthState>(listener: (context, state) {})];
  }

  _onGetLoginOtp() async {
    bool check = _formKey.currentState!.validate();
    if (check) {
      final phone = _phoneController.text;
      final rt = await _authRespository.getLoginOtp(
        phone: Utils.trimPhoneNumber(phone),
      );

      rt.fold((l) => {print('get login otp error: $l')}, (r) {
        // show otp dialog
        AppOtpDialog.show(
          title: context.localization.confirm_otp,
          message: context.localization.message_otp(phone),
          length: 4,

          onConfirm: (otp) {
            // Handle OTP confirmation
            print('OTP confirmed: $otp');

            return false;
          },
          onCancel: () {
            // Handle OTP cancellation
          },
          dismissible: true,
          onResendPress: () {
            // Handle OTP resend
          },
          timeoutSeconds: 60,
          errorMessage: context.localization.invalid_otp,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final paddingTop = (268 / 812) * AppDimensions.fullscreenHeight;
    return GestureDetector(
      onTap: () => Utils.hideKeyboard(),
      child: MultiBlocListener(
        listeners: _listener(),
        child: AppAnnotatedRegion(
          child: BackgroundContainer(
            bg: AssetImage(AppImages.loginBg),
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsetsGeometry.only(top: paddingTop),
                        child: Text(
                          context.localization.hi_saymee,
                          style: Styles.xxlarge.bold.copyWith(
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      24.verticalSpace,
                      Container(
                        padding: EdgeInsetsGeometry.only(left: 20),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          context.localization.label_phone,
                          style: Styles.medium.bold.copyWith(
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      8.verticalSpace,
                      Container(
                        padding: EdgeInsetsGeometry.only(left: 20, right: 20),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: TextInput(
                                keyboardType: TextInputType.number,
                                placeholder:
                                    context.localization.placeholder_phone,
                                controller: _phoneController,
                                validator: AppValidator.validatePhone,
                                errorMessage:
                                    context.localization.invalidPhoneNumber,
                                formKey: _formKey,
                              ),
                            ),
                            12.horizontalSpace,
                            SizedBox(
                              height: 44,
                              width: 44,
                              child: IconButton(
                                padding: EdgeInsets.zero,
                                icon: SvgPicture.asset(
                                  AppIcons.iconFaceid,
                                  width: 40,
                                  height: 40,
                                ),
                                onPressed: () => {
                                  /* TODO */
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      24.verticalSpace,
                      Container(
                        padding: EdgeInsetsGeometry.only(left: 20, right: 20),
                        child: PrimaryButton(
                          /// The line `// disabled: true,` is a commented-out line in the code snippet you
                          /// provided. Comments in code are used to provide explanations or notes for
                          /// developers and are not executed by the compiler or interpreter.
                          // disabled: true,
                          text: context.localization.login_otp,
                          onPress: _onGetLoginOtp,
                        ),
                      ),
                      12.verticalSpace,
                      Container(
                        padding: EdgeInsetsGeometry.only(left: 20, right: 20),
                        child: SecondaryButton(
                          /// The line `// disabled: true,` is a commented-out line in the code snippet you
                          /// provided. Comments in code are used to provide explanations or notes for
                          /// developers and are not executed by the compiler or interpreter.
                          // disabled: true,
                          text: context.localization.try_saymee,
                          onPress: () => {
                            /* TODO */
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
