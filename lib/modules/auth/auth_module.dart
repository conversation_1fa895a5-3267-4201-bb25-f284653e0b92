import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee_flutter/modules/auth/data/datasources/auth_api.dart';
import 'package:saymee_flutter/modules/auth/data/repositories/auth_repository.dart';
import 'package:saymee_flutter/modules/auth/general/auth_module_routes.dart';
import 'package:saymee_flutter/modules/auth/presentation/blocs/auth_bloc.dart';
import 'package:saymee_flutter/modules/auth/presentation/pages/sign_in_page.dart';

class AuthModule extends Module {
  @override
  void exportedBinds(Injector i) {
    super.exportedBinds(i);
    i.addSingleton(() => AuthApi());
    i.addSingleton(() => AuthRepository(api: Modular.get<AuthApi>()));

    // bloc
    i.addSingleton(() => AuthBloc(repository: Modular.get<AuthRepository>()));
  }

  @override
  void routes(RouteManager r) {
    super.routes(r);

    r.child(AuthModuleRoutes.signIn, child: (context) => const SignInPage());
  }
}
