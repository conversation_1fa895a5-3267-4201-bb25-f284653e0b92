{"@@locale": "en", "emailCannotEmpty": "Email cannot be empty", "invalidEmail": "Invalid email", "passwordCannotEmpty": "Password cannot be empty!", "passwordShorter6Character": "Password cannot be shorter than 6 characters!", "passwordError": "Passwords do not match, please enter again!", "phoneNumberCannotEmpty": "Phone number cannot be empty", "invalidPhoneNumber": "Invalid phone number", "pleaseTryAgain": "An error occurred, please try again.", "sessionHasExpired": "Session has expired!", "close": "Close", "notification": "Notification", "signing": "Signing", "signIn": "Sign in", "enterEmail": "Enter email", "password": "Password", "enterPassword": "Enter password", "forgotPassword": "Forgot password", "termsAndConditions": "Terms and conditions", "signOut": "Sign out", "cancel": "Cancel", "confirmSignOut": "Are you sure you want to log out of this account?", "success": "Success", "fullNameCannotEmpty": "Full name cannot be empty", "confirmPassword": "Confirm password", "goodMorning": "Good morning", "goodAfternoon": "Good afternoon", "goodEvening": "Good evening", "goodNight": "Good night", "usernameCannotEmpty": "Username is cannot empty!", "usernameInvalid": "<PERSON>rna<PERSON> invalid!", "otpCannotEmpty": "OTP cannot empty!", "groupNameCannotEmpty": "Group name cannot empty!", "confirmCreateRoom": "You already have a chat session with {name}. Would you like to continue existing session or start a new one ?", "@confirmCreateRoom": {"placeholders": {"name": {"type": "String", "example": "<PERSON><PERSON><PERSON>"}}}, "continueTitle": "Continue", "newSession": "New Session", "chatbotList": "Chat<PERSON> List", "onboardingContent1": "Group Chat with AI Chatbot", "onboardingContent2": "Effective advice in relationships", "onboardingContent3": "Ask ANYTHING with your friends", "recent": "Recent", "home": "Home", "chat": "Cha<PERSON>", "room": "Room", "profile": "Profile", "skip": "<PERSON><PERSON>", "activateAccountSuccess": "Activate account success", "activating": "Activating", "activateAccount": "Activate account", "otpSentToEmail": "An OTP has been sent to the email ", "otpToActivateAccount": ". Please use that OTP to activate your account.", "otpCode": "OTP Code", "inputOtpCode": "Input OTP Code", "enterEmailForResetPassword": "Enter your email address to receive the code and instructions to reset your password.", "inputYourEmail": "Input your email", "signUp": "Sign Up", "signUpWithEmail": "Sign up with <PERSON><PERSON>", "fullname": "Full name", "inputYourFullname": "Input your full name", "username": "Username", "chooseAnUsername": "Choose an username", "registering": "Registering", "resetPasswordSuccess": "Reset password success", "resetPassword": "Reset password", "otpToResetPassword": ". Please use that OTP to reset password.", "signInWithEmail": "Sign in with <PERSON><PERSON>", "signInWithApple": "Sign in with Apple", "notAccount": "You don’t have an account?", "signUpHere": " Sign up here", "byUsingService": "By using our services you are agreeing to our ", "terms": "Terms", "termAndConditions": "Term & Conditions", "and": " and ", "privacyPolicy": "Privacy Policy", "or": "Or", "yes": "Yes", "blockMember": "Block a member", "removeFromGroup": "Remove from group", "unblockMember": "Unblock a member", "selectChatbot": "Select Chatbot", "groupIntro": "Group Introduction", "save": "Save", "writeShortDescription": "Write a short descriptions", "search": "Search", "findGroupsToJoin": "Find groups to join", "yourPersonalFriend": "Your Personal A.I Companion", "yourPersonalFriendContent": "Engages in natural and meaningful conversations with humor, empathy, and creativity.", "startConversation": "Start a conversation", "chats": "Chats", "openSetting": "Open setting", "noPermission": "No permission", "pleaseOpenSetting": "Please open settings to grant access to camera", "howCanIHelpYou": "Hello, How can i help you?", "addMemberToCreateGroup": "Add member to create group", "clearContext": "Clear context", "clearMessage": "Clear Message", "clearMessageContent": "Clearing message data is permanent, the messages cannot be recovered.", "delete": "Delete", "confirmDelete": "Are you sure you want to delete?", "members": "members", "talkChatbot": "<PERSON> Chatbot", "creatingRoom": "Creating room", "createGroup": "Create Group", "done": "Done", "nameGroupChat": "Name group chat", "inputNameGroupChat": "Input name group chat", "uploadImage": "Upload image", "groupMembers": "Group Members", "updateSuccess": "Update success!", "clearSuccess": "Clear success!", "clear": "Clear", "mute": "Mute", "unmute": "Unmute", "leave": "Leave", "leaveGroup": "Leave Group", "leaveGroupContent": "Are you sure you want to leave the group?", "groupInfo": "Group info", "inviteLink": "Invite Link", "copyLink": "Copy Link", "copied": "<PERSON>pied", "writeDescription": "Write a short description and add photo of the group chat to introduce it to new users.", "nameOrAvatarGroup": "Name or avatar group", "updateNameOrAvatarGroup": "Updating the avatar and name makes the group easier to find and recognize.", "setGroupPrivate": "Set group as private", "setGroupPrivateContent": "The content of private group chats is not searchable within public channels, ensuring privacy for those conversations.", "memberRequest": "Member requests", "memberRequestContent": "Admins can approve requests to join group", "groupManage": "Group manage", "deleteGroup": "Delete group", "deleteGroupContent": "Deleting a group is permanent, all content will be irretrievably lost.", "confirmDeleteGroup": "Are you sure you want to delete group?", "groupSetting": "Group Settings", "requesting": "Requesting", "joining": "Joining", "rejecting": "Rejecting", "deny": "<PERSON><PERSON>", "accept": "Accept", "requested": "Requested", "joinTheGroup": "Join the group", "groupInvitation": "Group invitation", "imageDownloadedSuccess": "Image downloaded successfully!", "share": "Share", "roomIntro": "Experience the ultimate Group Chat with <PERSON><PERSON><PERSON>", "roomIntroContent": "Invite your friends to join the chat and chat with the AI as a group.", "createNewGroup": "Create a new group", "chatRoom": "Chat Room", "updating": "Updating", "credits": "credits", "forTitle": "for", "months": "months", "paymentSuccess": "Payment success", "paymentFailed": "Payment failed", "upgradeTo": "Upgrade to ", "premium": "premium", "plan": " plan", "andEnjoyAccess": "And enjoy access to all features of MindMate ", "noAds": "No ads", "accessModel": "Access to the smartest AI model", "fasterResponse": "Faster response times", "restorePurchases": "<PERSON><PERSON> Purchases", "subContent1": "Subscribed user has unlimited access to the services.", "subContent2": "Payment will be charged to iTunes Account at purchase confirmation", "subContent3": "Subscription automatically renews within 24-hours prior to the end of the current subscription period", "subContent4": "Subscription may be managed and auto-renewal may be turned off by going to the Users Account Settings after purchased", "subContent5": "Any unused portion of a free trial period, if offered,will be forfeited when user purchases a subscription to that publication, where applicable", "expiryDate": "Expiry date", "expired": "Expired", "creditsTitle": "Credits", "upgradeToPremium": "Upgrade to premium", "subscribeNow": "Subscribe Now", "accountInfo": "Account Information", "edit": "Edit", "setNewPhoto": "Set New Photo", "logout": "Log Out", "confirmLogout": "Are you sure you want to log out?", "appConfiguration": "App configuration", "store": "Store", "enhanceYourExperience": "Enhance your experience", "rateApp": "Rate app", "shareWithFriend": "Share with friend", "helpAndSupport": "Help and support", "faq": "FAQs", "contactUs": "Contact us", "copyRight": "Copyright ©2024 by MDC", "later": "Later", "upgrade": "Upgrade", "hi_saymee": "Hello <PERSON><PERSON> here!!", "placeholder_phone": "Enter your phone number", "label_phone": "Phone", "login_otp": "Login with OTP", "try_saymee": "<PERSON>", "confirm_otp": "Confirm OTP", "message_otp": "Confirmationc code has been sent to {phone}. Please enter", "@message_otp": {"placeholders": {"phone": {"type": "String", "example": "**********"}}}, "confirm": "Confirm", "otp_expire": "OTP expire in {seconds} seconds", "@otp_expire": {"placeholders": {"seconds": {"type": "int", "example": "30"}}}, "resend_otp": "Resend OTP", "invalid_otp": "Invalid OTP", "unknown_error": "Unknown error", "error_title": "Opps! Failed", "success_title": "Yay! Success"}