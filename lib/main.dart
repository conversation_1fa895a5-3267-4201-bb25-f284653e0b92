import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee_flutter/core/constants/app_environment.dart';
import 'package:saymee_flutter/core/helpers/general_helper.dart';
import 'package:saymee_flutter/main_module.dart';
import 'package:saymee_flutter/main_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await GeneralHelper.init();
  await dotenv.load(fileName: AppEnvironment.envFileName);
  final sharedPreferences = await SharedPreferences.getInstance();
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    ModularApp(
      module: MainModule(sharedPreferences: sharedPreferences),
      child: const MainWidget(),
    ),
  );
}
