import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee_flutter/core/components/app_dialog.dart';
import 'package:saymee_flutter/core/constants/app_keys.dart';
import 'package:saymee_flutter/core/constants/app_stores.dart';
import 'package:saymee_flutter/core/extensions/localized_extension.dart';
import 'package:saymee_flutter/core/helpers/general_helper.dart';
import 'package:saymee_flutter/core/helpers/shared_preference_helper.dart';
import 'package:saymee_flutter/core/utils/utils.dart';
import 'package:saymee_flutter/l10n/app_localizations.dart';

class DioInterceptor extends Interceptor {
  final _sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
  final Dio dio;

  DioInterceptor({required this.dio});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    String? accessToken = _sharedPreferenceHelper
        .get(key: AppStores.kAccessToken)
        ?.toString();

    if (accessToken != null && accessToken.isNotEmpty) {
      options.headers['apisecret'] = accessToken;
    }

    options.headers['Device-Id'] = GeneralHelper.deviceId;
    options.headers['App-Version'] = GeneralHelper.appVersion;
    options.headers['OS-Info'] = GeneralHelper.osInfo;
    options.headers['Device-Info'] = GeneralHelper.deviceInfo;
    options.headers['OS-Version'] = GeneralHelper.osVersion;

    options.headers['langcode'] = _sharedPreferenceHelper.get(
      key: AppStores.kAppLanguage,
    );
    options.headers['uuid'] = GeneralHelper.deviceId;
    options.headers['deviceinfo'] = GeneralHelper.deviceInfo;
    options.headers['appversion'] = GeneralHelper.appVersion;
    options.headers['buildnumber'] = GeneralHelper.buildNumber;
    options.headers['osinfo'] = GeneralHelper.osInfo;
    options.headers['mode'] = 'mobile';
    options.headers['accept-encoding'] =
        options.headers['accept-encoding'] ?? 'gzip';
    options.headers['content-type'] =
        options.headers['content-type'] ?? 'application/json';

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    Utils.debugLog(response.requestOptions.path);
    final statusCode = response.statusCode.toString();
    final Map<String, dynamic> mapData = response.data;
    final isError = mapData['errors'] != null || mapData['result'] == 'failed';
    if (isError) {
      final errorStr =
          mapData['errors'][0]['message'] ??
          mapData['reason'] ??
          mapData['message'] ??
          AppKeys.navigatorKey.currentContext?.localization.unknown_error;

      AppDialog.show(
        title: AppKeys.navigatorKey.currentContext?.localization.error_title,
        type: AppDialogType.failed,
        message: errorStr,
        confirmText: AppKeys.navigatorKey.currentContext?.localization.close,
      );
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    Utils.debugLog(err.requestOptions.path);
    /* show app dialog if error */
    if (dio.options.extra['notShowError'] != true) {
      final errorStr =
          err.response?.data['message'] ??
          AppKeys.navigatorKey.currentContext?.localization.unknown_error;
      AppDialog.show(
        title: AppKeys.navigatorKey.currentContext?.localization.error_title,
        type: AppDialogType.failed,
        message: errorStr,
        confirmText: AppKeys.navigatorKey.currentContext?.localization.close,
      );
    }
    return handler.reject(err);
  }
}
