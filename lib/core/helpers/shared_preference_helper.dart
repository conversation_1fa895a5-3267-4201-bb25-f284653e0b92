import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferenceHelper {
  final SharedPreferences sharedPreferences;

  SharedPreferenceHelper({required this.sharedPreferences});

  Future<void> set({required String key, required String value}) async {
    await sharedPreferences.setString(key, value);
  }

  Future<void> remove({required String key}) async {
    await sharedPreferences.remove(key);
  }

  Object? get({required String key}) {
    return sharedPreferences.get(key);
  }
}
