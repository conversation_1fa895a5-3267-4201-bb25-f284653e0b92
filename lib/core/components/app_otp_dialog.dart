import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee_flutter/core/components/buttons/outline_button.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_icons.dart';
import 'package:saymee_flutter/core/constants/app_keys.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:saymee_flutter/core/components/buttons/primary_button.dart';
import 'package:saymee_flutter/core/extensions/localized_extension.dart';

import 'package:saymee_flutter/core/extensions/num_extension.dart';

class AppOtpDialog {
  static void show({
    int length = 4,
    required String title,
    String? message,
    required Future<bool> Function(String) onConfirm,
    void Function()? onCancel,
    String? errorMessage,
    void Function()? onResendPress,
    bool dismissible = false,
    int? timeoutSeconds,
  }) {
    showDialog(
      barrierDismissible: dismissible,
      context: AppKeys.navigatorKey.currentContext!,
      builder: (context) {
        return PopScope(
          canPop: dismissible,
          child: _OtpDialogWidget(
            length: length,
            title: title,
            message: message,
            onConfirm: onConfirm,
            onCancel: onCancel,
            errorMessage: errorMessage,
            onResendPress: onResendPress,
            timeoutSeconds: timeoutSeconds,
          ),
        );
      },
    );
  }
}

class _OtpDialogWidget extends StatefulWidget {
  final int length;
  final String? title;
  final String? message;
  final Function(String) onConfirm;
  final VoidCallback? onCancel;
  final String? errorMessage;
  final VoidCallback? onResendPress;
  final int? timeoutSeconds;

  const _OtpDialogWidget({
    required this.length,
    this.title,
    this.message,
    required this.onConfirm,
    this.onCancel,
    this.errorMessage,
    this.onResendPress,
    this.timeoutSeconds,
  });

  @override
  State<_OtpDialogWidget> createState() => _OtpDialogWidgetState();
}

class _OtpDialogWidgetState extends State<_OtpDialogWidget> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  int _focusIndex = 0;
  String _currentError = '';
  Timer? _timer;
  int _remainingSeconds = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.length,
      (index) => TextEditingController(),
    );
    _focusNodes = List.generate(widget.length, (index) => FocusNode());

    // Initialize timer if timeout is provided
    if (widget.timeoutSeconds != null) {
      _remainingSeconds = widget.timeoutSeconds!;
      _startTimer();
    }

    // Add focus listeners to trigger UI updates
    for (int i = 0; i < _focusNodes.length; i++) {
      _focusNodes[i].addListener(() {
        setState(() {
          // This will trigger a rebuild to update border colors
        });
      });
    }

    // Auto-focus first input field when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_focusNodes.isNotEmpty) {
        _focusField(0);
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _timer?.cancel();
        }
      });
    });
  }

  void _onChanged(String value, int index) {
    setState(() {
      _currentError = ''; // Clear error when user types
    });

    if (value.isNotEmpty) {
      // Ensure only one character
      if (value.length > 1) {
        _controllers[index].text = value.substring(value.length - 1);
        _controllers[index].selection = TextSelection.fromPosition(
          TextPosition(offset: _controllers[index].text.length),
        );
        return;
      }

      // Move to next field with a slight delay to ensure proper focus
      if (index < widget.length - 1) {
        _focusField(index + 1);
      } else {
        // Last field, unfocus and check if OTP is complete
        _focusNodes[index].unfocus();
        _checkOtpComplete();
      }
    } else {
      if (index > 0) {
        _focusField(index - 1);
        _controllers[index - 1].selection = TextSelection(
          baseOffset: 0,
          extentOffset: _controllers[index - 1].text.length,
        );
      }
    }
  }

  void _checkOtpComplete() async {
    String otp = _controllers.map((controller) => controller.text).join();
    if (otp.length == widget.length) {
      setState(() {
        _isLoading = true;
      });
      final isTrue = await widget.onConfirm(otp);

      setState(() {
        _isLoading = false;
      });

      if (!isTrue) {
        setState(() {
          _currentError = widget.errorMessage!;
        });
      } else {
        _onClose();
      }
    }
  }

  void _onClose() {
    Navigator.of(context).pop();
  }

  void _clearAll() {
    for (var controller in _controllers) {
      controller.clear();
    }
    _focusField(0);
  }

  void _focusField(int index) {
    _focusNodes[index].requestFocus();
    setState(() {
      _focusIndex = index;
    });
  }

  bool _isConfirmDisabled() {
    String otp = _controllers.map((controller) => controller.text).join();
    return otp.length != widget.length;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.all(20),
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),

        child: Stack(
          children: [
            // Close button at top right
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: _onClose,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SvgPicture.asset(
                    AppIcons.iconClose,
                    colorFilter: ColorFilter.mode(
                      Colors.black,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
            // Main content
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                8.verticalSpace,

                // Title
                if (widget.title != null) ...[
                  Text(
                    widget.title!,
                    style: Styles.h5.smb.copyWith(color: Colors.black),
                    textAlign: TextAlign.center,
                  ),
                  8.verticalSpace,
                ],

                // Message
                if (widget.message != null) ...[
                  Text(
                    widget.message!,
                    style: Styles.medium.regular,
                    textAlign: TextAlign.center,
                  ),
                  12.verticalSpace,
                ],

                // OTP Input Fields
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  spacing: 8,
                  children: List.generate(
                    widget.length,
                    (index) => _buildOtpField(index),
                  ),
                ),

                // Error Message
                if (_currentError.isNotEmpty) ...[
                  8.verticalSpace,
                  Text(
                    _currentError,
                    style: Styles.small.regular.copyWith(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ],

                // Timeout Display
                if (widget.timeoutSeconds != null) ...[
                  8.verticalSpace,
                  Text(
                    context.localization.otp_expire(_remainingSeconds),
                    style: Styles.small.regular,
                    textAlign: TextAlign.center,
                  ),
                ],

                // Resend Button
                if (widget.onResendPress != null) ...[
                  8.verticalSpace,
                  GestureDetector(
                    onTap: () {
                      _clearAll();
                      widget.onResendPress!();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          context.localization.resend_otp,
                          style: Styles.small.regular.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                        Icon(
                          Icons.refresh_outlined,
                          color: AppColors.primary,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ],
                24.verticalSpace,

                // Buttons
                Stack(
                  children: [
                    PrimaryButton(
                      disabled: _isConfirmDisabled(),
                      text: context.localization.confirm,
                      onPress: _isLoading ? null : _checkOtpComplete,
                    ),
                    //add positioned loading indicator
                    if (_isLoading) ...[
                      Positioned(
                        right: 0,
                        child: SizedBox(
                          height: 12,
                          width: 12,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                12.verticalSpace,
                OutlineButton(
                  text: context.localization.close,
                  onPress: _onClose,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOtpField(int index) {
    return Container(
      width: 30,
      height: 36,
      padding: EdgeInsets.zero,
      alignment: Alignment.center,
      margin: EdgeInsetsGeometry.zero,
      decoration: BoxDecoration(
        border: Border.all(
          color: _currentError.isNotEmpty
              ? Colors.red
              : (_focusNodes[index].hasFocus
                    ? AppColors.primary
                    : AppColors.secondaryText),
          width: 1.2,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: TextField(
        cursorColor: AppColors.primary,
        controller: _controllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: Styles.medium.regular,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(1),
        ],

        decoration: const InputDecoration(
          border: InputBorder.none,
          isCollapsed: true,
          isDense: true,
          counterText: '',
          contentPadding: EdgeInsets.only(left: 2),
        ),
        onChanged: (value) => _onChanged(value, index),
        onTap: () {
          // Clear error when user taps on field
          if (_currentError.isNotEmpty) {
            setState(() {
              _currentError = '';
            });
          }

          // Ensure this field gets focus
          if (!_focusNodes[index].hasFocus) {
            _focusField(index);
          }

          // Select all text when tapping on field
          _controllers[index].selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controllers[index].text.length,
          );
        },
        onEditingComplete: () {
          // Handle backspace navigation
          if (_controllers[index].text.isEmpty && index > 0) {
            _focusField(index - 1);
          }
        },
      ),
    );
  }
}
