import 'package:flutter/material.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';

class SecondaryButton extends StatelessWidget {
  final double? height;
  final String text;
  final void Function()? onPress;
  final List<Color>? gradient;

  const SecondaryButton({
    super.key,
    this.height,
    required this.text,
    required this.onPress,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return InnerShadow(
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.64),
          blurRadius: 4,
          offset: Offset(-2, 2),
        ),
      ],
      child: Container(
        width: double.infinity,
        height: height ?? 44,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(44)),
          gradient: LinearGradient(
            colors: gradient ?? AppColors.secondaryGradient,
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0, 1],
          ),
        ),
        child: Text<PERSON><PERSON><PERSON>(
          onPressed: onPress,
          child: Text(
            text,
            style: Styles.medium.smb.copyWith(color: Colors.white),
          ),
        ),
      ),
    );
  }
}
