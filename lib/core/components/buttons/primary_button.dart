import 'package:flutter/material.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';

class PrimaryButton extends StatelessWidget {
  final double? height;
  final String text;
  final void Function()? onPress;
  final bool disabled;
  final List<Color>? gradient;

  const PrimaryButton({
    super.key,
    this.height,
    required this.text,
    required this.onPress,
    this.disabled = false,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return InnerShadow(
      shadows: [
        Shadow(
          color: Colors.white.withValues(alpha: 0.6),
          blurRadius: 8,
          offset: Offset(-2, 2),
        ),
      ],
      child: Container(
        width: double.infinity,
        height: height ?? 44,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(44)),
          gradient: LinearGradient(
            colors: disabled
                ? AppColors.disabledGradient
                : gradient ?? AppColors.primaryGradient,
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0, 1],
          ),
        ),
        child: TextButton(
          onPressed: disabled ? null : onPress,
          child: Text(
            text,
            style: Styles.medium.smb.copyWith(color: Colors.white),
          ),
        ),
      ),
    );
  }
}
