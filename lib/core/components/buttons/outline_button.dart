import 'package:flutter/material.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';

class OutlineButton extends StatelessWidget {
  final double? height;
  final String text;
  final void Function()? onPress;
  final List<Color>? gradient;

  const OutlineButton({
    super.key,
    this.height,
    required this.text,
    required this.onPress,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return InnerShadow(
      child: Container(
        width: double.infinity,
        height: height ?? 44,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary, width: 1.2),
          borderRadius: BorderRadius.all(Radius.circular(44)),
          gradient: LinearGradient(
            colors: [Colors.transparent, Colors.transparent],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0, 1],
          ),
        ),
        child: TextButton(
          onPressed: onPress,
          child: Text(
            text,
            style: Styles.medium.smb.copyWith(color: AppColors.primary),
          ),
        ),
      ),
    );
  }
}
