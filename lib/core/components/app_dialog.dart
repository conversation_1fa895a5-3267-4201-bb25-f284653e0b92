import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:saymee_flutter/core/components/buttons/outline_button.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_icons.dart';
import 'package:saymee_flutter/core/constants/app_keys.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:saymee_flutter/core/components/buttons/primary_button.dart';
import 'package:saymee_flutter/core/extensions/num_extension.dart';
import 'package:saymee_flutter/core/extensions/localized_extension.dart';

enum AppDialogType { success, failed }

class AppDialog {
  static void show({
    required String title,
    String? message,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool dismissible = true,
    AppDialogType? type,
  }) {
    showDialog(
      barrierDismissible: dismissible,
      context: AppKeys.navigatorKey.currentContext!,
      builder: (context) {
        return PopScope(
          canPop: dismissible,
          child: _AppDialogWidget(
            title: title,
            message: message,
            confirmText: confirmText,
            cancelText: cancelText,
            onConfirm: onConfirm,
            onCancel: onCancel,
            type: type,
          ),
        );
      },
    );
  }
}

class _AppDialogWidget extends StatelessWidget {
  final String? title;
  final String? message;
  final String? confirmText;
  final String? cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final AppDialogType? type;

  const _AppDialogWidget({
    this.title,
    this.message,
    this.confirmText,
    this.cancelText,
    this.onConfirm,
    this.onCancel,
    this.type,
  });

  @override
  Widget build(BuildContext context) {
    final typeColor = type == AppDialogType.success
        ? AppColors.success
        : AppColors.fail;

    void _onClose() {
      Navigator.of(context).pop();
    }

    return Dialog(
      insetPadding: EdgeInsets.all(20),
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),

        child: Stack(
          children: [
            // Close button at top right
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: _onClose,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SvgPicture.asset(
                    AppIcons.iconClose,
                    width: 16,
                    height: 16,
                    colorFilter: ColorFilter.mode(
                      Colors.black54,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                8.verticalSpace,
                // Title
                Text(
                  title!,
                  style: Styles.h5.smb.copyWith(
                    color: type != null ? typeColor : Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                8.verticalSpace,
                // show mascot icon if type!=null here
                if (type != null) ...[
                  SvgPicture.asset(
                    type == AppDialogType.success
                        ? AppIcons.iconMascotSuccess
                        : AppIcons.iconMascotFail,
                  ),
                  8.verticalSpace,
                ],

                // Message
                if (message != null) ...[
                  Text(
                    message!,
                    style: Styles.medium.regular,
                    textAlign: TextAlign.center,
                  ),
                  24.verticalSpace,
                ],
                // Buttons
                PrimaryButton(
                  text: confirmText ?? context.localization.close,
                  onPress: () {
                    _onClose();
                    onConfirm?.call();
                  },
                ),
                if (cancelText != null) ...[
                  12.verticalSpace,
                  OutlineButton(text: cancelText!, onPress: _onClose),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
