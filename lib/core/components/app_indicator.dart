import 'package:flutter/material.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_keys.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';

class AppIndicator {
  static bool _isShowing = false;

  /// Show loading indicator with dimmed background
  static void show({bool dissmissible = false}) {
    if (_isShowing) return; // Prevent multiple indicators

    _isShowing = true;
    showDialog(
      context: AppKeys.navigatorKey.currentContext!,
      barrierDismissible: dissmissible,
      barrierColor: Colors.black.withOpacity(0.5), // Dimmed background
      builder: (context) {
        return PopScope(canPop: false, child: _LoadingIndicatorWidget());
      },
    );
  }

  /// Hide loading indicator
  static void hide() {
    if (!_isShowing) return;

    _isShowing = false;
    final context = AppKeys.navigatorKey.currentContext;
    if (context != null && Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  /// Check if indicator is currently showing
  static bool get isShowing => _isShowing;
}

class _LoadingIndicatorWidget extends StatelessWidget {
  const _LoadingIndicatorWidget();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Primary color circular progress indicator
            SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
