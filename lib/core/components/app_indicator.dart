import 'package:flutter/material.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_keys.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';

class AppIndicator {
  static bool _isShowing = false;
  static BuildContext? _dialogContext;

  /// Show loading indicator with dimmed background
  static void show({bool dissmissible = false}) {
    if (_isShowing) return; // Prevent multiple indicators

    _isShowing = true;
    showDialog(
      context: AppKeys.navigatorKey.currentContext!,
      barrierDismissible: dissmissible,
      barrierColor: Colors.black.withValues(alpha: 0.5), // Dimmed background
      builder: (context) {
        _dialogContext = context; // Store the specific dialog context
        return PopScope(canPop: dissmissible, child: _LoadingIndicatorWidget());
      },
    ).then((_) {
      // Reset when dialog is dismissed
      _isShowing = false;
      _dialogContext = null;
    });
  }

  /// Hide loading indicator specifically
  static void hide() {
    if (!_isShowing || _dialogContext == null) return;

    _isShowing = false;
    // Use the specific dialog context to close only this dialog
    Navigator.of(_dialogContext!).pop();
    _dialogContext = null;
  }

  /// Check if indicator is currently showing
  static bool get isShowing => _isShowing;
}

class _LoadingIndicatorWidget extends StatelessWidget {
  const _LoadingIndicatorWidget();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Primary color circular progress indicator
            SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
