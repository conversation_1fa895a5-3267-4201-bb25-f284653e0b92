import 'package:flutter/material.dart';

extension IntExtensions on int {
  /// <PERSON>em tra so chan
  bool get isEven => this % 2 == 0;

  /// <PERSON>em tra so le
  bool get isOdd => this % 2 != 0;

  /// Kiem tra so > 0
  bool get isPositive => this > 0;

  /// Kiem tra so < 0
  bool get isNegative => this < 0;

  /// <PERSON><PERSON><PERSON> sang kieu boolean
  /// Tra ve true neu so > 0
  bool get asBool => this > 0;

  // ---- Chuyen doi thanh Duration ----
  Duration toMilliseconds() => Duration(milliseconds: this);

  Duration toSeconds() => Duration(seconds: this);

  Duration toMinutes() => Duration(minutes: this);

  Duration toHours() => Duration(hours: this);

  Duration toDays() => Duration(days: this);
  // ---- end ----

  // Spacer
  Spacer get toSpacer => Spacer(flex: this);
}
