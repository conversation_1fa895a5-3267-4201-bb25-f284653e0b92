import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:saymee_flutter/core/constants/app_configs.dart';
import 'package:saymee_flutter/core/helpers/general_helper.dart';

class AppEnvironment {
  AppEnvironment._();

  static String get envFileName =>
      AppConfigs.flavorDev == GeneralHelper.appFlavor
      ? '.env.development'
      : '.env.production';

  static String get saymeeApiUrl => dotenv.env['SAYMEE_API_URL'] ?? '';
  static String get cmsApiUrl => dotenv.env['CMS_API_URL'] ?? '';
  static String get baseUrl => dotenv.env['BASE_URL'] ?? saymeeApiUrl;
}
