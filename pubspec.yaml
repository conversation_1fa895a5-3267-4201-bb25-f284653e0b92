name: saymee_flutter
description: "A new Flutter project."
publish_to: "none"
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  bloc: ^9.0.0
  crypto: ^3.0.6
  dartz: ^0.10.1
  device_info_plus: ^11.5.0
  dio: ^5.8.0+1
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_dotenv: ^5.2.1
  flutter_inner_shadow: ^0.0.1
  flutter_localizations:
    sdk: flutter
  flutter_modular: ^6.4.1
  flutter_portal: ^1.1.4
  flutter_svg: ^2.2.0
  fluttertoast: ^8.2.12
  intl: ^0.20.2
  package_info_plus: ^8.3.0
  shared_preferences: ^2.5.3
  url_launcher: ^6.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  generate: true


  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - .env.development
    - .env.production
    - assets/images/
    - assets/icons/
    - assets/fonts/


  fonts:
    - family: BeVietnamPro
      fonts:
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Bold.ttf
          weight: 900
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Medium.ttf
          weight: 500
        - asset: assets/fonts/BeVietnamPro/BeVietnamPro-Regular.ttf
          weight: 400
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
